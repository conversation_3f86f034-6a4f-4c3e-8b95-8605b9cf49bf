import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:nordic_nrf_mesh_faradine/nordic_nrf_mesh_faradine.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart' as ble;

void main() {
  runApp(const ESP32MeshProvisionerApp());
}

class ESP32MeshProvisionerApp extends StatelessWidget {
  const ESP32MeshProvisionerApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 BLE Mesh Provisioner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const MeshProvisionerPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MeshProvisionerPage extends StatefulWidget {
  const MeshProvisionerPage({Key? key}) : super(key: key);

  @override
  State<MeshProvisionerPage> createState() => _MeshProvisionerPageState();
}

class _MeshProvisionerPageState extends State<MeshProvisionerPage> {
  // Core mesh components
  late NordicNrfMesh nordicNrfMesh;
  late MeshManagerApi meshManagerApi;
  late BleMeshManager bleMeshManager;

  // State variables
  IMeshNetwork? meshNetwork;
  List<ble.DiscoveredDevice> discoveredDevices = [];
  List<ProvisionedMeshNode> provisionedNodes = [];

  bool isScanning = false;
  bool isInitialized = false;
  bool isProvisioning = false;
  String statusMessage = 'Initializing...';

  // Stream subscriptions
  StreamSubscription<IMeshNetwork?>? networkUpdateSubscription;
  StreamSubscription<IMeshNetwork?>? networkImportSubscription;
  StreamSubscription<IMeshNetwork?>? networkLoadSubscription;
  StreamSubscription<List<ble.DiscoveredDevice>>? scanSubscription;

  @override
  void initState() {
    super.initState();
    initializeMesh();
  }

  @override
  void dispose() {
    networkUpdateSubscription?.cancel();
    networkImportSubscription?.cancel();
    networkLoadSubscription?.cancel();
    scanSubscription?.cancel();
    super.dispose();
  }

  Future<void> initializeMesh() async {
    try {
      setState(() {
        statusMessage = 'Requesting permissions...';
      });

      await requestPermissions();

      setState(() {
        statusMessage = 'Initializing mesh components...';
      });

      nordicNrfMesh = NordicNrfMesh();
      meshManagerApi = nordicNrfMesh.meshManagerApi;
      bleMeshManager = BleMeshManager();

      bleMeshManager.callbacks = ESP32MeshManagerCallbacks(meshManagerApi);

      setState(() {
        statusMessage = 'Setting up network listeners...';
      });

      setupNetworkListeners();

      setState(() {
        statusMessage = 'Creating mesh network...';
      });

      await createOrLoadMeshNetwork();

      setState(() {
        isInitialized = true;
        statusMessage = 'Ready to scan for ESP32 devices';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Initialization failed: $e';
      });
      print('Mesh initialization error: $e');
    }
  }

  Future<void> requestPermissions() async {
    final permissions = [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.bluetoothAdvertise,
      Permission.location,
      Permission.locationWhenInUse,
    ];

    for (final permission in permissions) {
      final status = await permission.status;
      if (status.isDenied || status.isPermanentlyDenied) {
        final result = await permission.request();
        if (result.isPermanentlyDenied) {
          throw Exception('Permission $permission is permanently denied. Please enable it in settings.');
        }
        if (result.isDenied) {
          throw Exception('Permission $permission is required for BLE Mesh functionality.');
        }
      }
    }
  }

  void setupNetworkListeners() {
    networkUpdateSubscription = meshManagerApi.onNetworkUpdated.listen((network) async {
      setState(() {
        meshNetwork = network;
      });
      final currentNetwork = network;
      if (currentNetwork != null) {
        try {
          final nodes = await currentNetwork.nodes;
          setState(() {
            provisionedNodes = nodes;
          });
        } catch (e) {
          print('Error getting nodes: $e');
        }
      }
    });

    networkImportSubscription = meshManagerApi.onNetworkImported.listen((network) async {
      setState(() {
        meshNetwork = network;
      });
      final currentNetwork = network;
      if (currentNetwork != null) {
        try {
          final nodes = await currentNetwork.nodes;
          setState(() {
            provisionedNodes = nodes;
          });
        } catch (e) {
          print('Error getting nodes: $e');
        }
      }
    });

    networkLoadSubscription = meshManagerApi.onNetworkLoaded.listen((network) async {
      setState(() {
        meshNetwork = network;
      });
      final currentNetwork = network;
      if (currentNetwork != null) {
        try {
          final nodes = await currentNetwork.nodes;
          setState(() {
            provisionedNodes = nodes;
          });
        } catch (e) {
          print('Error getting nodes: $e');
        }
      }
    });
  }

  Future<void> createOrLoadMeshNetwork() async {
    try {
      final network = await meshManagerApi.loadMeshNetwork();
      if (network != null) {
        setState(() {
          meshNetwork = network;
        });
        try {
          final nodes = await network.nodes;
          setState(() {
            provisionedNodes = nodes;
          });
        } catch (e) {
          print('Error getting nodes from loaded network: $e');
        }
        return;
      }
    } catch (e) {
      print('No existing network found, creating new one: $e');
    }

    final networkJson = createESP32MeshNetwork();
    final network = await meshManagerApi.importMeshNetworkJson(networkJson);

    setState(() {
      meshNetwork = network;
    });

    final currentNetwork = network;
    if (currentNetwork != null) {
      try {
        final nodes = await currentNetwork.nodes;
        setState(() {
          provisionedNodes = nodes;
        });
      } catch (e) {
        print('Error getting nodes from new network: $e');
      }
    }
  }

  String createESP32MeshNetwork() {
    final networkData = {
      "version": "1.0.0",
      "meshUUID": "12345678-1234-5678-9abc-123456789abc",
      "meshName": "ESP32 BLE Mesh Network",
      "timestamp": DateTime.now().toIso8601String(),
      "partial": false,
      "netKeys": [
        {
          "name": "ESP32 Primary NetKey",
          "index": 0,
          "phase": 0,
          "minSecurity": "secure",
          "timestamp": DateTime.now().toIso8601String(),
          "key": "7dd7364cd842ad18c17c2b820c84c3d6",
          "keyRefresh": 0,
          "oldKey": "00000000000000000000000000000000"
        }
      ],
      "appKeys": [
        {
          "name": "ESP32 OnOff AppKey",
          "index": 0,
          "boundNetKey": 0,
          "key": "63964771734fbd76e3b40519d1d94a48"
        }
      ],
      "provisioners": [
        {
          "provisionerName": "Flutter ESP32 Provisioner",
          "UUID": "*************-8765-dcba-987654321abc",
          "allocatedUnicastRange": [
            {"lowAddress": "0001", "highAddress": "7FFF"}
          ],
          "allocatedGroupRange": [
            {"lowAddress": "C000", "highAddress": "FEFF"}
          ],
          "allocatedSceneRange": [
            {"firstScene": "0001", "lastScene": "FFFF"}
          ]
        }
      ],
      "nodes": [],
      "groups": [
        {
          "name": "ESP32 OnOff Group",
          "address": "C000",
          "parentAddress": "0000"
        }
      ],
      "scenes": [],
      "networkExclusions": [],
      "ivIndex": 0,
      "features": {
        "friend": 0,
        "lowPower": 0,
        "proxy": 1,
        "relay": 1
      },
      "ttl": 7
    };

    return jsonEncode(networkData);
  }

  Future<void> startScanning() async {
    if (isScanning || !isInitialized) return;

    setState(() {
      isScanning = true;
      discoveredDevices.clear();
      statusMessage = 'Scanning for ESP32 BLE Mesh devices...';
    });

    try {
      scanSubscription?.cancel();

      scanSubscription = nordicNrfMesh.scanForUnprovisionedNodes().listen(
        (devices) {
          if (devices is List<ble.DiscoveredDevice>) {
            for (final device in devices) {
              if (isESP32Device(device)) {
                setState(() {
                  if (!discoveredDevices.any((d) => d.id == device.id)) {
                    discoveredDevices.add(device);
                  }
                  statusMessage = 'Found ${discoveredDevices.length} ESP32 device(s)';
                });
              }
            }
          }
        },
        onError: (error) {
          setState(() {
            statusMessage = 'Scan error: $error';
          });
          print('Scan error: $error');
        },
      );

      Timer(const Duration(seconds: 30), stopScanning);
    } catch (e) {
      setState(() {
        isScanning = false;
        statusMessage = 'Scan failed: $e';
      });
      print('Scan failed: $e');
    }
  }

  bool isESP32Device(ble.DiscoveredDevice device) {
    final meshProvisioningUuid = ble.Uuid.parse('1827');
    if (device.serviceUuids.contains(meshProvisioningUuid)) {
      return true;
    }

    final name = device.name.toLowerCase();
    return name.contains('esp32') ||
           name.contains('esp') ||
           name.contains('mesh') ||
           name.isEmpty;
  }

  void stopScanning() {
    if (!isScanning) return;

    scanSubscription?.cancel();
    setState(() {
      isScanning = false;
      statusMessage = 'Scan stopped. Found ${discoveredDevices.length} devices.';
    });
  }

  Future<void> provisionESP32Device(ble.DiscoveredDevice device) async {
    if (meshNetwork == null) {
      setState(() {
        statusMessage = 'No mesh network available';
      });
      return;
    }

    if (isProvisioning) {
      setState(() {
        statusMessage = 'Already provisioning a device';
      });
      return;
    }

    setState(() {
      isProvisioning = true;
      statusMessage = 'Provisioning ESP32 device: ${device.name.isEmpty ? device.id : device.name}...';
    });

    try {
      final serviceDataUuid = "1827";
      final provisioningEvents = ProvisioningEvent();

      final provisionedNode = await nordicNrfMesh.provisioning(
        meshManagerApi,
        bleMeshManager,
        device,
        serviceDataUuid,
        events: provisioningEvents,
      );

      await configureESP32OnOffServer(provisionedNode);

      setState(() {
        statusMessage = 'ESP32 device ready for OnOff commands!';
        isProvisioning = false;
        provisionedNodes.add(provisionedNode);
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Provisioning failed: $e';
        isProvisioning = false;
      });
      print('Provisioning error: $e');
    }
  }

  Future<void> configureESP32OnOffServer(ProvisionedMeshNode node) async {
    try {
      setState(() {
        statusMessage = 'Configuring ESP32 OnOff Server...';
      });

      await Future.delayed(const Duration(seconds: 2));

      // Use app key index 0 for configuration
      final appKeyIndex = 0;

      setState(() {
        statusMessage = 'Adding application key to ESP32...';
      });

      // Get the unicast address
      final unicastAddress = await node.unicastAddress;

      await meshManagerApi.sendConfigAppKeyAdd(unicastAddress, appKeyIndex);
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        statusMessage = 'Binding OnOff Server model...';
      });

      await meshManagerApi.sendConfigModelAppBind(
        unicastAddress,
        0x0000,
        0x1000,
      );

      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        statusMessage = 'ESP32 OnOff Server configured successfully!';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'ESP32 configuration failed: $e';
      });
      print('ESP32 configuration error: $e');
    }
  }

  Future<void> sendESP32OnOffCommand(ProvisionedMeshNode node, bool onOff) async {
    try {
      setState(() {
        statusMessage = 'Sending ${onOff ? 'ON' : 'OFF'} command to ESP32...';
      });

      // Use app key index 0 for commands
      final appKeyIndex = 0;

      // Get the unicast address
      final unicastAddress = await node.unicastAddress;

      await meshManagerApi.sendGenericOnOffSet(
        unicastAddress,
        0x0000,
        onOff,
      );

      setState(() {
        statusMessage = 'ESP32 ${onOff ? 'ON' : 'OFF'} command sent successfully!';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'ESP32 command failed: $e';
      });
      print('ESP32 OnOff command error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ESP32 BLE Mesh Provisioner'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: Icon(isScanning ? Icons.stop : Icons.bluetooth_searching),
            onPressed: isInitialized ? (isScanning ? stopScanning : startScanning) : null,
            tooltip: isScanning ? 'Stop Scanning' : 'Scan for ESP32',
          ),
        ],
      ),
      body: Column(
        children: [
          Card(
            margin: const EdgeInsets.all(16),
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        isInitialized ? Icons.check_circle : Icons.hourglass_empty,
                        color: isInitialized ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Status',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    statusMessage,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: !isInitialized || isScanning ? null : startScanning,
                        icon: const Icon(Icons.bluetooth_searching),
                        label: const Text('Scan ESP32'),
                      ),
                      ElevatedButton.icon(
                        onPressed: isScanning ? stopScanning : null,
                        icon: const Icon(Icons.stop),
                        label: const Text('Stop'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  TabBar(
                    tabs: [
                      Tab(
                        icon: const Icon(Icons.devices_other),
                        text: 'Unprovisioned (${discoveredDevices.length})',
                      ),
                      Tab(
                        icon: const Icon(Icons.device_hub),
                        text: 'Provisioned (${provisionedNodes.length})',
                      ),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        buildUnprovisionedDevicesList(),
                        buildProvisionedNodesList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildUnprovisionedDevicesList() {
    if (discoveredDevices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_disabled,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              isScanning
                ? 'Scanning for ESP32 devices...'
                : 'No ESP32 devices found',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              isScanning
                ? 'Make sure your ESP32 is running the onoff_server example'
                : 'Tap "Scan ESP32" to search for devices',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: discoveredDevices.length,
      itemBuilder: (context, index) {
        final device = discoveredDevices[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: 2,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue,
              child: const Icon(Icons.memory, color: Colors.white),
            ),
            title: Text(
              device.name.isEmpty ? 'ESP32 Device' : device.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('ID: ${device.id}'),
                Text('RSSI: ${device.rssi} dBm'),
                if (device.serviceUuids.isNotEmpty)
                  Text('Services: ${device.serviceUuids.length}'),
              ],
            ),
            trailing: ElevatedButton.icon(
              onPressed: isProvisioning ? null : () => provisionESP32Device(device),
              icon: const Icon(Icons.add_link),
              label: const Text('Provision'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget buildProvisionedNodesList() {
    if (provisionedNodes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.device_hub,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No provisioned ESP32 devices',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Provision an ESP32 device to control it',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: provisionedNodes.length,
      itemBuilder: (context, index) {
        final node = provisionedNodes[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: 2,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green,
              child: const Icon(Icons.check, color: Colors.white),
            ),
            title: Text(
              'ESP32 OnOff Server',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FutureBuilder<int>(
                  future: node.unicastAddress,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Text('Address: 0x${snapshot.data!.toRadixString(16).toUpperCase().padLeft(4, '0')}');
                    }
                    return const Text('Address: Loading...');
                  },
                ),
                Text('UUID: ${node.uuid}'),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ElevatedButton.icon(
                  onPressed: () => sendESP32OnOffCommand(node, true),
                  icon: const Icon(Icons.lightbulb),
                  label: const Text('ON'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.black,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => sendESP32OnOffCommand(node, false),
                  icon: const Icon(Icons.lightbulb_outline),
                  label: const Text('OFF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }
}

class ESP32MeshManagerCallbacks extends BleMeshManagerCallbacks {
  final MeshManagerApi meshManagerApi;

  ESP32MeshManagerCallbacks(this.meshManagerApi);

  @override
  Future<void> sendMtuToMeshManagerApi(int mtu) async {
    print('MTU for ESP32 device: $mtu');
  }
}
