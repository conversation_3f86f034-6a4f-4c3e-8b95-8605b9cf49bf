import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:nordic_nrf_mesh_faradine/nordic_nrf_mesh_faradine.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 BLE Mesh Provisioner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const MeshProvisionerPage(),
    );
  }
}

class MeshProvisionerPage extends StatefulWidget {
  const MeshProvisionerPage({Key? key}) : super(key: key);

  @override
  State<MeshProvisionerPage> createState() => _MeshProvisionerPageState();
}

class _MeshProvisionerPageState extends State<MeshProvisionerPage> {
  late NordicNrfMesh nordicNrfMesh;
  late MeshManagerApi meshManagerApi;
  late BleMeshManager bleMeshManager;

  IMeshNetwork? meshNetwork;
  List<DiscoveredDevice> discoveredDevices = [];
  List<ProvisionedMeshNode> provisionedNodes = [];

  bool isScanning = false;
  bool isInitialized = false;
  String statusMessage = 'Initializing...';

  StreamSubscription<IMeshNetwork?>? networkUpdateSubscription;
  StreamSubscription<IMeshNetwork?>? networkImportSubscription;
  StreamSubscription<IMeshNetwork?>? networkLoadSubscription;
  StreamSubscription<List<DiscoveredDevice>>? scanSubscription;
  StreamSubscription<BluetoothDeviceState>? connectionSubscription;

  @override
  void initState() {
    super.initState();
    initializeMesh();
  }

  @override
  void dispose() {
    networkUpdateSubscription?.cancel();
    networkImportSubscription?.cancel();
    networkLoadSubscription?.cancel();
    scanSubscription?.cancel();
    connectionSubscription?.cancel();
    super.dispose();
  }

  Future<void> initializeMesh() async {
    try {
      // Request permissions
      await requestPermissions();

      // Initialize Nordic nRF Mesh
      nordicNrfMesh = NordicNrfMesh();
      meshManagerApi = nordicNrfMesh.meshManagerApi;
      bleMeshManager = BleMeshManager();

      // Set up callbacks
      bleMeshManager.callbacks = MeshManagerCallbacks(meshManagerApi);

      // Set up network listeners
      setupNetworkListeners();

      // Create or load mesh network
      await createOrLoadMeshNetwork();

      setState(() {
        isInitialized = true;
        statusMessage = 'Mesh network initialized';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Initialization failed: $e';
      });
    }
  }

  Future<void> requestPermissions() async {
    final permissions = [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.bluetoothAdvertise,
      Permission.location,
      Permission.locationWhenInUse,
    ];

    for (final permission in permissions) {
      if (await permission.isDenied) {
        await permission.request();
      }
    }
  }

  void setupNetworkListeners() {
    networkUpdateSubscription =
        meshManagerApi.onNetworkUpdated.listen((network) {
      setState(() {
        meshNetwork = network;
        if (network != null) {
          provisionedNodes = network.nodes;
        }
      });
    });

    networkImportSubscription =
        meshManagerApi.onNetworkImported.listen((network) {
      setState(() {
        meshNetwork = network;
        if (network != null) {
          provisionedNodes = network.nodes;
        }
      });
    });

    networkLoadSubscription = meshManagerApi.onNetworkLoaded.listen((network) {
      setState(() {
        meshNetwork = network;
        if (network != null) {
          provisionedNodes = network.nodes;
        }
      });
    });
  }

  Future<void> createOrLoadMeshNetwork() async {
    try {
      // Try to load existing network first
      final network = await meshManagerApi.loadMeshNetwork();
      if (network != null) {
        setState(() {
          meshNetwork = network;
          provisionedNodes = network.nodes;
        });
        return;
      }
    } catch (e) {
      print('No existing network found, creating new one');
    }

    // Create new mesh network
    final networkJson = createDefaultMeshNetwork();
    final network = await meshManagerApi.importMeshNetworkJson(networkJson);

    setState(() {
      meshNetwork = network;
      if (network != null) {
        provisionedNodes = network.nodes;
      }
    });
  }

  String createDefaultMeshNetwork() {
    final networkData = {
      "version": "1.0.0",
      "meshUUID": "00000000-0000-0000-0000-000000000001",
      "meshName": "ESP32 Mesh Network",
      "timestamp": DateTime.now().toIso8601String(),
      "partial": false,
      "netKeys": [
        {
          "name": "Primary NetKey",
          "index": 0,
          "phase": 0,
          "minSecurity": "secure",
          "timestamp": DateTime.now().toIso8601String(),
          "key": "7dd7364cd842ad18c17c2b820c84c3d6",
          "keyRefresh": 0,
          "oldKey": "00000000000000000000000000000000"
        }
      ],
      "appKeys": [
        {
          "name": "Primary AppKey",
          "index": 0,
          "boundNetKey": 0,
          "key": "63964771734fbd76e3b40519d1d94a48"
        }
      ],
      "provisioners": [
        {
          "provisionerName": "Flutter Provisioner",
          "UUID": "00000000-0000-0000-0000-000000000002",
          "allocatedUnicastRange": [
            {"lowAddress": "0001", "highAddress": "7FFF"}
          ],
          "allocatedGroupRange": [
            {"lowAddress": "C000", "highAddress": "FEFF"}
          ],
          "allocatedSceneRange": [
            {"firstScene": "0001", "lastScene": "FFFF"}
          ]
        }
      ],
      "nodes": [],
      "groups": [
        {"name": "All Devices", "address": "C000", "parentAddress": "0000"}
      ],
      "scenes": [],
      "networkExclusions": [],
      "ivIndex": 0,
      "features": {"friend": 0, "lowPower": 0, "proxy": 1, "relay": 1},
      "ttl": 5
    };

    return jsonEncode(networkData);
  }

  Future<void> startScanning() async {
    if (isScanning) return;

    setState(() {
      isScanning = true;
      discoveredDevices.clear();
      statusMessage = 'Scanning for unprovisioned devices...';
    });

    try {
      scanSubscription?.cancel();
      scanSubscription = nordicNrfMesh.scanForUnprovisionedNodes().listen(
        (devices) {
          setState(() {
            discoveredDevices = devices;
            statusMessage = 'Found ${devices.length} unprovisioned devices';
          });
        },
        onError: (error) {
          setState(() {
            statusMessage = 'Scan error: $error';
          });
        },
      );

      // Stop scanning after 30 seconds
      Timer(const Duration(seconds: 30), stopScanning);
    } catch (e) {
      setState(() {
        isScanning = false;
        statusMessage = 'Scan failed: $e';
      });
    }
  }

  void stopScanning() {
    if (!isScanning) return;

    scanSubscription?.cancel();
    setState(() {
      isScanning = false;
      statusMessage =
          'Scan stopped. Found ${discoveredDevices.length} devices.';
    });
  }

  Future<void> provisionDevice(DiscoveredDevice device) async {
    if (meshNetwork == null) {
      setState(() {
        statusMessage = 'No mesh network available';
      });
      return;
    }

    setState(() {
      statusMessage = 'Provisioning device: ${device.name}...';
    });

    try {
      // Get the service data UUID for ESP32 BLE Mesh
      // ESP32 uses 0x1827 for Mesh Provisioning Service
      final serviceDataUuid = "1827";

      final provisioningEvents = ProvisioningEvent(
        onProvisioningStateChanged: (state, data) {
          setState(() {
            statusMessage = 'Provisioning state: $state';
          });
        },
        onProvisioningFailed: (error) {
          setState(() {
            statusMessage = 'Provisioning failed: $error';
          });
        },
        onProvisioningCompleted: (node) {
          setState(() {
            statusMessage = 'Provisioning completed for node: ${node.nodeName}';
            provisionedNodes.add(node);
          });
        },
        onProvisioningCapabilities: (capabilities) {
          setState(() {
            statusMessage = 'Received capabilities: $capabilities';
          });
        },
      );

      final provisionedNode = await nordicNrfMesh.provisioning(
        meshManagerApi,
        bleMeshManager,
        device,
        serviceDataUuid,
        events: provisioningEvents,
      );

      // Configure the provisioned node
      await configureProvisionedNode(provisionedNode);

      setState(() {
        statusMessage =
            'Device provisioned successfully: ${provisionedNode.nodeName}';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Provisioning failed: $e';
      });
    }
  }

  Future<void> configureProvisionedNode(ProvisionedMeshNode node) async {
    try {
      // Connect to the provisioned node
      await bleMeshManager.connect(
        DiscoveredDevice(
          id: node.uuid,
          name: node.nodeName ?? 'Unknown',
          serviceData: {},
          manufacturerData: Uint8List(0),
          rssi: -50,
        ),
      );

      // Wait for connection
      await Future.delayed(const Duration(seconds: 2));

      // Add application key to the node
      if (meshNetwork?.appKeys.isNotEmpty == true) {
        final appKey = meshNetwork!.appKeys.first;
        await meshManagerApi.sendConfigAppKeyAdd(node, appKey);

        // Wait for response
        await Future.delayed(const Duration(seconds: 1));

        // Bind app key to Generic OnOff Server model
        // Model ID for Generic OnOff Server is 0x1000
        await meshManagerApi.sendConfigModelAppBind(
          node,
          0x0000, // Element index
          0x1000, // Generic OnOff Server model ID
          appKey,
        );
      }

      // Set publication address for the Generic OnOff Server
      // Use group address C000 (All Devices)
      final publicationSettings = MeshPublication(
        publishAddress: 0xC000,
        appKeyIndex: 0,
        credentialFlag: false,
        publishTtl: 5,
        publishPeriod: 0,
        retransmitCount: 0,
        retransmitIntervalSteps: 0,
      );

      await meshManagerApi.sendConfigModelPublicationSet(
        node,
        0x0000, // Element index
        0x1000, // Generic OnOff Server model ID
        publicationSettings,
      );

      setState(() {
        statusMessage = 'Node configured successfully';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Configuration failed: $e';
      });
    }
  }

  Future<void> sendOnOffCommand(ProvisionedMeshNode node, bool onOff) async {
    try {
      setState(() {
        statusMessage = 'Sending ${onOff ? 'ON' : 'OFF'} command...';
      });

      if (meshNetwork?.appKeys.isNotEmpty == true) {
        final appKey = meshNetwork!.appKeys.first;

        await meshManagerApi.sendGenericOnOffSet(
          node,
          0x0000, // Element index
          appKey,
          onOff,
          acknowledgement: true,
        );

        setState(() {
          statusMessage = 'Command sent successfully';
        });
      }
    } catch (e) {
      setState(() {
        statusMessage = 'Command failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ESP32 BLE Mesh Provisioner'),
        actions: [
          IconButton(
            icon: Icon(isScanning ? Icons.stop : Icons.search),
            onPressed: isScanning ? stopScanning : startScanning,
          ),
        ],
      ),
      body: Column(
        children: [
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(statusMessage),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: !isInitialized ? null : startScanning,
                        child: const Text('Scan'),
                      ),
                      ElevatedButton(
                        onPressed: isScanning ? stopScanning : null,
                        child: const Text('Stop'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'Unprovisioned'),
                      Tab(text: 'Provisioned'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        buildUnprovisionedDevicesList(),
                        buildProvisionedNodesList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildUnprovisionedDevicesList() {
    return ListView.builder(
      itemCount: discoveredDevices.length,
      itemBuilder: (context, index) {
        final device = discoveredDevices[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            title: Text(device.name.isEmpty ? 'Unknown Device' : device.name),
            subtitle: Text('ID: ${device.id}\nRSSI: ${device.rssi}'),
            trailing: ElevatedButton(
              onPressed: () => provisionDevice(device),
              child: const Text('Provision'),
            ),
          ),
        );
      },
    );
  }

  Widget buildProvisionedNodesList() {
    return ListView.builder(
      itemCount: provisionedNodes.length,
      itemBuilder: (context, index) {
        final node = provisionedNodes[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            title: Text(node.nodeName ?? 'Unknown Node'),
            subtitle: Text(
                'Address: ${node.unicastAddress.toRadixString(16).toUpperCase()}'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ElevatedButton(
                  onPressed: () => sendOnOffCommand(node, true),
                  child: const Text('ON'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => sendOnOffCommand(node, false),
                  child: const Text('OFF'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class MeshManagerCallbacks extends BleMeshManagerCallbacks {
  final MeshManagerApi meshManagerApi;

  MeshManagerCallbacks(this.meshManagerApi);

  @override
  void onDeviceConnected(DiscoveredDevice device) {
    print('Device connected: ${device.name}');
  }

  @override
  void onDeviceDisconnected(DiscoveredDevice device) {
    print('Device disconnected: ${device.name}');
  }

  @override
  void onDeviceReady(DiscoveredDevice device) {
    print('Device ready: ${device.name}');
  }

  @override
  void onMeshPduCreated(Uint8List pdu) {
    print('Mesh PDU created: ${pdu.length} bytes');
  }

  @override
  void onNetworkPduReceived(Uint8List pdu) {
    print('Network PDU received: ${pdu.length} bytes');
  }
}
