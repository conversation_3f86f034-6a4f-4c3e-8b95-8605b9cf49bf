# ESP32 BLE Mesh Provisioner

A professional Flutter application for provisioning and controlling ESP32 devices running BLE Mesh OnOff Server examples.

## Features

- **ESP32 BLE Mesh Support**: Specifically designed for ESP32 devices running the onoff_server example
- **Professional UI**: Modern Material Design 3 interface with clear status indicators
- **Automatic Device Detection**: Filters and identifies ESP32 BLE Mesh devices automatically
- **Complete Provisioning Flow**: Full provisioning process with real-time status updates
- **OnOff Control**: Send ON/OFF commands to provisioned ESP32 devices
- **Error Handling**: Comprehensive error handling and user feedback

## Prerequisites

### ESP32 Setup
1. Flash your ESP32 with the BLE Mesh onoff_server example from ESP-IDF:
   ```
   https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/esp_ble_mesh/onoff_models/onoff_server
   ```

2. Make sure your ESP32 is advertising with the mesh provisioning service (UUID: 0x1827)

### Flutter Setup
1. Flutter SDK 3.0.0 or higher
2. Android device with BLE support
3. Location and Bluetooth permissions

## Installation

1. Clone or download this project
2. Run `flutter pub get` to install dependencies
3. For Android, ensure your `android/settings.gradle` includes the Nordic nRF Mesh plugin settings
4. Build and run on your Android device

## Usage

### 1. Initialize the App
- Launch the app
- Grant all required permissions (Bluetooth, Location)
- Wait for "Ready to scan for ESP32 devices" status

### 2. Scan for ESP32 Devices
- Tap "Scan ESP32" button
- The app will automatically filter for ESP32 BLE Mesh devices
- Discovered devices will appear in the "Unprovisioned" tab

### 3. Provision an ESP32 Device
- In the "Unprovisioned" tab, tap "Provision" on an ESP32 device
- Watch the real-time provisioning status
- The device will automatically be configured for OnOff Server functionality

### 4. Control ESP32 Devices
- Switch to the "Provisioned" tab
- Use "ON" and "OFF" buttons to control the ESP32 device
- The ESP32 should respond by toggling its LED or other connected output

## Technical Details

### BLE Mesh Configuration
- **Network Name**: ESP32 BLE Mesh Network
- **Provisioning Service UUID**: 0x1827
- **Generic OnOff Server Model ID**: 0x1000
- **Group Address**: 0xC000 (ESP32 OnOff Group)
- **App Key Index**: 0

### Supported ESP32 Models
- ESP32
- ESP32-S3
- ESP32-C3
- ESP32-C6
- Any ESP32 variant with BLE Mesh support

## Troubleshooting

### Common Issues

1. **No devices found during scan**
   - Ensure ESP32 is running the onoff_server example
   - Check that ESP32 is not already provisioned
   - Verify Bluetooth and Location permissions are granted

2. **Provisioning fails**
   - Reset the ESP32 and try again
   - Ensure the ESP32 is in range (< 10 meters)
   - Check that no other provisioner is trying to connect

3. **OnOff commands don't work**
   - Verify the ESP32 was properly configured during provisioning
   - Check that the ESP32 is still connected to the mesh network
   - Try reprovisioning the device

### Debug Information
The app provides detailed logging in the console. Check the debug output for:
- Provisioning state changes
- BLE connection status
- Mesh PDU transmission
- Error messages

## Dependencies

- `nordic_nrf_mesh_faradine: ^0.13.4` - BLE Mesh functionality
- `flutter_reactive_ble: ^5.0.2` - BLE operations
- `permission_handler: ^11.3.1` - Permission management

## License

This project is provided as-is for educational and development purposes.
